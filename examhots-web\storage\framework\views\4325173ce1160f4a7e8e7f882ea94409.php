
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
    <div class="flex items-center space-x-4">
        <!-- Question <PERSON>con -->
        <div
            class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-900"><PERSON><PERSON><PERSON></h3>
            <p class="text-gray-600 text-sm mt-1">Buat soal dan jawaban untuk melengkapi bank soal</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeQuestionTypeModal" onclick="closeModal()" type="button"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>

<!-- Progress Bar -->
<div class="px-6 py-2">
    <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-primary-blue h-2 rounded-full w-full"></div>
    </div>
</div>

<form
    action="<?php echo e($mode === 'edit'
        ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
        : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type])); ?>"
    method="POST">
    <?php echo csrf_field(); ?>
    <?php if($mode === 'edit'): ?>
        <?php echo method_field('PUT'); ?>
    <?php endif; ?>
    <!-- Modal Body -->
    <div class="px-6 py-6 space-y-4">
        <input type="hidden" name="uploaded_images" id="uploadedImages"
            value="<?php echo e($mode === 'edit' ? $question->img : ''); ?>">

        <div id="myDropzone" class="dropzone mb-3"></div>

        <div class="mb-3">
            <label for="question_text" class="block text-sm font-medium text-gray-700">
                Pertanyaan <span class="text-red-500">*</span>
            </label>
            <div id="question_editor" style="height: 200px;"></div>
            <textarea name="question" id="question_text" style="display: none;"><?php echo e(old('question', $question->question ?? '')); ?></textarea>
            <?php $__errorArgs = ['question'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-red-500 text-sm"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-4">
            <label for="answer" class="block text-sm font-medium text-gray-700">
                Jawaban <span class="text-red-500">*</span>
            </label>

            <?php for($i = 1; $i <= 5; $i++): ?>
                <?php
                    $letter = chr(64 + $i);
                ?>
                <div class="flex items-start space-x-3 mb-3">
                    <input type="radio" name="correct" id="answer<?php echo e($i); ?>" value="<?php echo e($letter); ?>"
                        class="mt-2" <?php if(old('correct', isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->is_correct : false)): ?> checked <?php endif; ?>>

                    
                    

                    <div class="flex-1">
                        <label for="answer<?php echo e($i); ?>"
                            class="text-sm font-medium text-gray-700 mb-1 block"><?php echo e($letter); ?></label>
                        
                        <div id="answer_editor_<?php echo e($i); ?>" style="height: 100px;"></div>
                        <textarea name="answer_<?php echo e($i); ?>" id="answer_text_<?php echo e($i); ?>" style="display: none;"><?php echo e(old("answer_$i", isset($question->answers[$i - 1]) ? $question->answers[$i - 1]->answer : '')); ?></textarea>
                        <?php $__errorArgs = ["answer_$i"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-red-500 text-sm"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>

    <!-- Modal Footer -->
    <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
        <button id="cancelQuestionType" onclick="closeModal()" type="button"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="continueQuestionType" type="submit"
            class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50">
            <?php echo e($mode === 'edit' ? 'Simpan Perubahan' : 'Simpan'); ?>

        </button>
    </div>
</form>

<script>
    console.log('🚀 MULTIPLE CHOICE FORM SCRIPT STARTING...');
    console.log('🔍 Current URL:', window.location.href);
    console.log('🔍 Document ready state:', document.readyState);

    // Initialize immediately when script loads
    console.log('📝 Multiple choice form script loaded');

    // Try multiple initialization approaches
    function tryInitialization() {
        console.log('🔄 Attempting initialization...');

        // First initialize standard editors (question + answer A)
        if (typeof window.initializeQuillEditors === 'function') {
            console.log('✅ Calling initializeQuillEditors...');
            window.initializeQuillEditors();
        } else {
            console.warn('⚠️ initializeQuillEditors not available');
        }

        // Then initialize additional answer editors
        initializeMultipleChoiceAnswers();
    }

    // Try initialization immediately
    setTimeout(tryInitialization, 100);

    // Also try when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM ready, trying initialization...');
        setTimeout(tryInitialization, 300);
    });

    // Also try if DOM is already ready
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        console.log('📄 DOM already ready, trying initialization...');
        setTimeout(tryInitialization, 100);
    }

    // Function to initialize Quill editors for answers B, C, D, E
    function initializeMultipleChoiceAnswers() {
        console.log('🔧 Starting initializeMultipleChoiceAnswers...');

        if (typeof Quill === 'undefined') {
            console.error('❌ Quill is not loaded for multiple choice answers');
            return;
        }

        console.log('✅ Quill is available for multiple choice answers');

        // Initialize editors for answers 2-5 (B, C, D, E)
        for (let i = 2; i <= 5; i++) {
            const editorId = 'answer_editor_' + i;
            const textareaId = 'answer_text_' + i;

            console.log(`🔍 Looking for elements: ${editorId} and ${textareaId}`);

            const editorElement = document.getElementById(editorId);
            const textareaElement = document.getElementById(textareaId);

            console.log(`🔍 Found elements: editor=${!!editorElement}, textarea=${!!textareaElement}`);

            if (editorElement && textareaElement) {
                try {
                    // Create Quill editor
                    const quill = new Quill('#' + editorId, {
                        theme: 'snow',
                        modules: {
                            toolbar: [
                                ['bold', 'italic', 'underline'],
                                [{
                                    'script': 'sub'
                                }, {
                                    'script': 'super'
                                }],
                                ['formula'],
                                ['clean']
                            ]
                        }
                    });

                    // Set initial content
                    const initialContent = textareaElement.value;
                    if (initialContent) {
                        quill.root.innerHTML = initialContent;
                    }

                    // Update textarea when content changes
                    quill.on('text-change', function() {
                        textareaElement.value = quill.root.innerHTML;
                    });

                    // Store reference for form submission
                    window['quillAnswer' + i] = quill;

                    console.log('✅ Initialized Quill editor for answer ' + i);
                } catch (error) {
                    console.error('❌ Error initializing Quill editor for answer ' + i + ':', error);
                }
            } else {
                console.warn(
                    `⚠️ Missing elements for answer ${i}: editor=${!!editorElement}, textarea=${!!textareaElement}`);
            }
        }

        // Handle form submission
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function() {
                // Update all answer textareas before submission
                for (let i = 2; i <= 5; i++) {
                    const quill = window['quillAnswer' + i];
                    const textarea = document.getElementById('answer_text_' + i);
                    if (quill && textarea) {
                        textarea.value = quill.root.innerHTML;
                    }
                }
            });
        }
    }

    // Make function globally available
    window.initializeMultipleChoiceAnswers = initializeMultipleChoiceAnswers;

    // Aggressive initialization - keep trying until elements are found
    let initAttempts = 0;
    const maxAttempts = 20;

    function aggressiveInit() {
        initAttempts++;
        console.log(`🔄 Aggressive init attempt ${initAttempts}/${maxAttempts}`);

        const questionEditor = document.getElementById('question_editor');
        const answerEditor2 = document.getElementById('answer_editor_2');

        if (questionEditor && answerEditor2) {
            console.log('✅ Elements found! Running initialization...');
            tryInitialization();
            return true; // Stop trying
        } else {
            console.log(`⏳ Elements not ready yet (question: ${!!questionEditor}, answer2: ${!!answerEditor2})`);
            if (initAttempts < maxAttempts) {
                setTimeout(aggressiveInit, 200);
            } else {
                console.error('❌ Max attempts reached, elements still not found');
            }
            return false;
        }
    }

    // Start aggressive initialization
    setTimeout(aggressiveInit, 100);
</script>
<?php /**PATH C:\xampp-8.2-new\htdocs\examhots\examhots-web\resources\views/admin/exam/questionbank/form/form_pilihan_ganda.blade.php ENDPATH**/ ?>