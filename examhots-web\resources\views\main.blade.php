<!doctype html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? 'ExamHots' }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <style>
        iconify-icon {
            display: inline-block;
            color: #455A9D;
        }
    </style>
    <link rel="stylesheet" href="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.css') }}">
    <link rel="stylesheet" href="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.css') }}">
    <!-- Quill.js CSS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'manrope': ['Manrope', 'sans-serif'],
                    },
                    colors: {
                        'primary-blue': '#455A9D',
                        'secondary-blue': '#6366F1',
                    }
                }
            }
        }
    </script>
    <!-- Iconify Script -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}">
    <style>
        .dropzone .dz-preview .dz-image {
            border-radius: 8px;
            width: 120px;
            height: 120px;
            overflow: hidden;
        }

        .dropzone .dz-preview .dz-image img {
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
    </style>
    <!-- Quill.js CSS and JS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
</head>

<body class="font-manrope bg-gray-50">
    <!-- Header -->
    <header class="relative bg-cover bg-center bg-no-repeat px-6 py-4"
        style="background-image: url('{{ asset('assets/img/bg-login.svg') }}');">
        <!-- Background overlay for better readability -->
        <div class="absolute inset-0 bg-white/10"></div>

        <div class="relative flex items-center justify-between">
            <!-- Left side - Navigation buttons -->
            <div class="flex items-center space-x-2">
                <a href="{{ route('dashboard') }}"
                    class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm {{ request()->routeIs('dashboard') ? 'bg-white text-primary-blue' : '' }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z">
                        </path>
                    </svg>
                    <span class="font-medium">Dashboard</span>
                </a>
                <a href="{{ route('question.index') }}"
                    class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm {{ request()->routeIs('question.*') ? 'bg-white text-primary-blue' : '' }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    <span class="font-medium">Bank Soal</span>
                </a>
                <a href="{{ route('exam.index') }}"
                    class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm {{ request()->routeIs('exam.*') ? 'bg-white text-primary-blue' : '' }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7">
                        </path>
                    </svg>
                    <span class="font-medium">Jadwal Ujian</span>
                </a>

                <!-- Management Dropdown (Admin Only) -->
                @if (Auth::check() && Auth::user()->role === 'admin')
                    <div class="relative">
                        <button id="managementButton"
                            class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm {{ request()->routeIs(['class.*', 'teachers.*', 'student.*', 'users.*']) ? 'bg-white text-primary-blue' : '' }}">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                            <span class="font-medium">Manajemen</span>
                            <svg class="w-4 h-4 transition-transform" id="managementChevron" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7">
                                </path>
                            </svg>
                        </button>

                        <!-- Management Dropdown Menu -->
                        <div id="managementDropdown"
                            class="absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                            <!-- Management Menu Items -->
                            <div class="py-1">
                                <a href="{{ route('users.index') }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors {{ request()->routeIs('users.*') ? 'bg-blue-50 text-primary-blue' : '' }}">
                                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                        </path>
                                    </svg>
                                    Manajemen User
                                </a>
                                <a href="{{ route('teachers.index') }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors {{ request()->routeIs('teachers.*') ? 'bg-blue-50 text-primary-blue' : '' }}">
                                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                        </path>
                                    </svg>
                                    Manajemen Guru
                                </a>
                                <a href="{{ route('class.index') }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors {{ request()->routeIs('class.*') ? 'bg-blue-50 text-primary-blue' : '' }}">
                                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                        </path>
                                    </svg>
                                    Manajemen Kelas
                                </a>
                                <a href="{{ route('student.index') }}"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors {{ request()->routeIs('student.*') ? 'bg-blue-50 text-primary-blue' : '' }}">
                                    <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                        </path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                        </path>
                                    </svg>
                                    Manajemen Siswa
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Center - Logo -->
            <div class="flex items-center">
                <img src="{{ asset('assets/img/logo-um.png') }}" alt="Logo UM" class="w-12 h-12">
            </div>

            <!-- Right side - Notification and Profile -->
            <div class="flex items-center space-x-3">
                <!-- Notification Button -->
                <div class="relative">
                    <button
                        class="text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-white/20 transition-colors relative">
                        <iconify-icon icon="iconamoon:notification" width="24" height="24"></iconify-icon>
                        <!-- Notification Badge -->
                        <span
                            class="notification-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                </div>

                <!-- Profile Dropdown -->
                <div class="relative">
                    <button id="profileButton"
                        class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 p-1 rounded-lg hover:bg-white/20 transition-colors">
                        <div
                            class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm">
                            <img src="{{ asset('assets/img/profile.jpg') }}" alt="Profile"
                                class="w-full h-full object-cover">
                        </div>
                        <iconify-icon icon="mdi:chevron-down" width="16" height="16"
                            id="chevronIcon"></iconify-icon>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="profileDropdown"
                        class="profile-dropdown absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                        <!-- User Info -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm flex-shrink-0">
                                    <img src="{{ asset('assets/img/profile.jpg') }}" alt="Profile"
                                        class="w-full h-full object-cover">
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ Auth::user()->name ?? 'User' }}</p>
                                    <p class="text-xs text-gray-500 truncate">
                                        {{ Auth::user()->email ?? '<EMAIL>' }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:account-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Profil Saya
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:cog" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Pengaturan
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:help-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Bantuan
                            </a>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-100 my-1">

                        <!-- Logout -->
                        <div class="py-1">
                            <form action="{{ route('logout') }}" method="POST" id="logoutForm">
                                @csrf
                                <button type="button" onclick="handleLogout()"
                                    class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <iconify-icon icon="mdi:logout" width="16" height="16"
                                        class="mr-3 text-red-600"></iconify-icon>
                                    Keluar
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container-fluid px-6 py-8">
        @yield('content')
    </main>

    <div id="modalGlobal"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto mx-auto transform transition-all duration-300 scale-95 opacity-0"
            id="modalContent">

        </div>
    </div>

    <div id="responseModal"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto mx-auto transform transition-all duration-300 scale-95 opacity-0"
            id="responseContent">

        </div>
    </div>

    <!-- SweetAlert2 JS -->
    <script src="{{ asset('package/dist/libs/sweetalert2/dist/sweetalert2.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.js') }}"></script>
    @yield('script')
    <script>
        // Profile Dropdown Toggle
        const profileButton = document.getElementById('profileButton');
        const profileDropdown = document.getElementById('profileDropdown');
        const chevronIcon = document.getElementById('chevronIcon');

        if (profileButton && profileDropdown && chevronIcon) {
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();

                // Toggle dropdown visibility
                profileDropdown.classList.toggle('hidden');

                // Rotate chevron icon
                if (profileDropdown.classList.contains('hidden')) {
                    chevronIcon.style.transform = 'rotate(0deg)';
                } else {
                    chevronIcon.style.transform = 'rotate(180deg)';
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileButton.contains(e.target) && !profileDropdown.contains(e.target)) {
                    profileDropdown.classList.add('hidden');
                    chevronIcon.style.transform = 'rotate(0deg)';
                }
            });

            // Close dropdown when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    profileDropdown.classList.add('hidden');
                    chevronIcon.style.transform = 'rotate(0deg)';
                }
            });

            // Add smooth transition for chevron rotation
            chevronIcon.style.transition = 'transform 0.2s ease-in-out';
        }

        // Management Dropdown Toggle
        const managementButton = document.getElementById('managementButton');
        const managementDropdown = document.getElementById('managementDropdown');
        const managementChevron = document.getElementById('managementChevron');

        if (managementButton && managementDropdown && managementChevron) {
            managementButton.addEventListener('click', function(e) {
                e.stopPropagation();

                // Close profile dropdown if open
                if (profileDropdown && !profileDropdown.classList.contains('hidden')) {
                    profileDropdown.classList.add('hidden');
                    if (chevronIcon) chevronIcon.style.transform = 'rotate(0deg)';
                }

                // Toggle management dropdown visibility
                managementDropdown.classList.toggle('hidden');

                // Rotate chevron icon
                if (managementDropdown.classList.contains('hidden')) {
                    managementChevron.style.transform = 'rotate(0deg)';
                } else {
                    managementChevron.style.transform = 'rotate(180deg)';
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!managementButton.contains(e.target) && !managementDropdown.contains(e.target)) {
                    managementDropdown.classList.add('hidden');
                    managementChevron.style.transform = 'rotate(0deg)';
                }
            });

            // Close dropdown when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    managementDropdown.classList.add('hidden');
                    managementChevron.style.transform = 'rotate(0deg)';
                }
            });

            // Add smooth transition for chevron rotation
            managementChevron.style.transition = 'transform 0.2s ease-in-out';
        }

        // Function untuk handle logout
        function handleLogout() {
            if (typeof Swal === 'undefined') {
                // Fallback ke confirm browser default
                if (confirm('Apakah Anda yakin ingin keluar?')) {
                    document.getElementById('logoutForm').submit();
                }
                return;
            }

            try {
                Swal.fire({
                    title: 'Konfirmasi Logout',
                    text: "Apakah Anda yakin ingin keluar?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#455A9D',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, Logout!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById('logoutForm').submit();
                    }
                });
            } catch (error) {
                // Fallback ke confirm browser default
                if (confirm('Apakah Anda yakin ingin keluar?')) {
                    document.getElementById('logoutForm').submit();
                }
            }
        }
    </script>

    <!-- Quill.js JavaScript -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

    <!-- Global Quill Initialization Function -->
    <script>
        // Global function to initialize Quill editors
        window.initializeQuillEditors = function() {
            // Check if Quill is available
            if (typeof Quill === 'undefined') {
                return false;
            }

            // Look for question editor
            const questionEditor = document.getElementById('question_editor');
            const answerEditor = document.getElementById('answer_editor');
            const questionText = document.getElementById('question_text');
            const answerText = document.getElementById('answer_text');

            if (!questionEditor || !answerEditor || !questionText || !answerText) {
                return false;
            }

            try {
                // Destroy existing instances if they exist
                if (window.quillQuestion) {
                    delete window.quillQuestion;
                }
                if (window.quillAnswer) {
                    delete window.quillAnswer;
                }

                // Initialize Quill editor for question
                window.quillQuestion = new Quill('#question_editor', {
                    theme: 'snow',
                    modules: {
                        toolbar: [
                            [{
                                'header': [1, 2, 3, false]
                            }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{
                                'color': []
                            }, {
                                'background': []
                            }],
                            [{
                                'script': 'sub'
                            }, {
                                'script': 'super'
                            }],
                            [{
                                'list': 'ordered'
                            }, {
                                'list': 'bullet'
                            }],
                            [{
                                'indent': '-1'
                            }, {
                                'indent': '+1'
                            }],
                            [{
                                'align': []
                            }],
                            ['formula'],
                            ['clean']
                        ]
                    }
                });

                // Set initial content for question
                var initialContent = questionText.value;
                if (initialContent) {
                    window.quillQuestion.root.innerHTML = initialContent;
                }

                // Update hidden textarea when content changes
                window.quillQuestion.on('text-change', function() {
                    questionText.value = window.quillQuestion.root.innerHTML;
                });

                // Initialize Quill editor for answer
                window.quillAnswer = new Quill('#answer_editor', {
                    theme: 'snow',
                    modules: {
                        toolbar: [
                            [{
                                'header': [1, 2, 3, false]
                            }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{
                                'color': []
                            }, {
                                'background': []
                            }],
                            [{
                                'script': 'sub'
                            }, {
                                'script': 'super'
                            }],
                            [{
                                'list': 'ordered'
                            }, {
                                'list': 'bullet'
                            }],
                            [{
                                'indent': '-1'
                            }, {
                                'indent': '+1'
                            }],
                            [{
                                'align': []
                            }],
                            ['formula'],
                            ['clean']
                        ]
                    }
                });

                // Set initial content for answer
                var initialAnswerContent = answerText.value;
                if (initialAnswerContent) {
                    window.quillAnswer.root.innerHTML = initialAnswerContent;
                }

                // Update hidden textarea when answer content changes
                window.quillAnswer.on('text-change', function() {
                    answerText.value = window.quillAnswer.root.innerHTML;
                });

                // Update Quill when form is submitted
                const form = document.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function() {
                        if (window.quillQuestion) {
                            questionText.value = window.quillQuestion.root.innerHTML;
                        }
                        if (window.quillAnswer) {
                            answerText.value = window.quillAnswer.root.innerHTML;
                        }
                    });
                }

                return true;

            } catch (error) {
                return false;
            }
        };

        // Global function to initialize Quill editors specifically for pilihan ganda (multiple choice)
        window.initializePilihanGandaQuillEditors = function() {
            console.log('🔧 Starting initializePilihanGandaQuillEditors...');

            // Check if Quill is available
            if (typeof Quill === 'undefined') {
                console.error('❌ Quill is not loaded for pilihan ganda');
                return false;
            }

            console.log('✅ Quill is available for pilihan ganda');

            try {
                // Initialize question editor first
                const questionEditor = document.getElementById('question_editor');
                const questionText = document.getElementById('question_text');

                if (questionEditor && questionText) {
                    console.log('✅ Found question editor elements');

                    // Destroy existing instance if it exists
                    if (window.quillQuestion) {
                        delete window.quillQuestion;
                    }

                    // Initialize Quill editor for question
                    window.quillQuestion = new Quill('#question_editor', {
                        theme: 'snow',
                        modules: {
                            toolbar: [
                                [{
                                    'header': [1, 2, 3, false]
                                }],
                                ['bold', 'italic', 'underline', 'strike'],
                                [{
                                    'color': []
                                }, {
                                    'background': []
                                }],
                                [{
                                    'script': 'sub'
                                }, {
                                    'script': 'super'
                                }],
                                [{
                                    'list': 'ordered'
                                }, {
                                    'list': 'bullet'
                                }],
                                [{
                                    'indent': '-1'
                                }, {
                                    'indent': '+1'
                                }],
                                [{
                                    'align': []
                                }],
                                ['formula'],
                                ['clean']
                            ]
                        }
                    });

                    // Set initial content for question
                    const initialQuestionContent = questionText.value;
                    if (initialQuestionContent) {
                        window.quillQuestion.root.innerHTML = initialQuestionContent;
                    }

                    // Update hidden textarea when question content changes
                    window.quillQuestion.on('text-change', function() {
                        questionText.value = window.quillQuestion.root.innerHTML;
                    });

                    console.log('✅ Initialized question editor');
                } else {
                    console.warn('⚠️ Question editor elements not found');
                }

                // Then initialize answer editors for A, B, C, D, E
                // Initialize editors for answers 1-5 (A, B, C, D, E)
                for (let i = 1; i <= 5; i++) {
                    const editorId = 'answer_editor_' + i;
                    const textareaId = 'answer_text_' + i;

                    console.log(`🔍 Looking for elements: ${editorId} and ${textareaId}`);

                    const editorElement = document.getElementById(editorId);
                    const textareaElement = document.getElementById(textareaId);

                    console.log(`🔍 Found elements: editor=${!!editorElement}, textarea=${!!textareaElement}`);

                    if (editorElement && textareaElement) {
                        try {
                            // Destroy existing instance if it exists
                            if (window['quillAnswer' + i]) {
                                delete window['quillAnswer' + i];
                            }

                            // Create Quill editor
                            const quill = new Quill('#' + editorId, {
                                theme: 'snow',
                                modules: {
                                    toolbar: [
                                        ['bold', 'italic', 'underline'],
                                        [{
                                            'script': 'sub'
                                        }, {
                                            'script': 'super'
                                        }],
                                        ['formula'],
                                        ['clean']
                                    ]
                                }
                            });

                            // Set initial content
                            const initialContent = textareaElement.value;
                            if (initialContent) {
                                quill.root.innerHTML = initialContent;
                            }

                            // Update textarea when content changes
                            quill.on('text-change', function() {
                                textareaElement.value = quill.root.innerHTML;
                            });

                            // Store reference for form submission
                            window['quillAnswer' + i] = quill;

                            console.log('✅ Initialized Quill editor for answer ' + i);
                        } catch (error) {
                            console.error('❌ Error initializing Quill editor for answer ' + i + ':', error);
                        }
                    } else {
                        console.warn(
                            `⚠️ Missing elements for answer ${i}: editor=${!!editorElement}, textarea=${!!textareaElement}`
                        );
                    }
                }

                // Handle form submission for pilihan ganda
                const form = document.querySelector('form');
                if (form) {
                    // Remove existing event listener if any
                    form.removeEventListener('submit', window.pilihanGandaSubmitHandler);

                    // Create new submit handler
                    window.pilihanGandaSubmitHandler = function() {
                        console.log('📝 Form submission - updating all answer textareas...');
                        // Update all answer textareas before submission
                        for (let i = 2; i <= 5; i++) {
                            const quill = window['quillAnswer' + i];
                            const textarea = document.getElementById('answer_text_' + i);
                            if (quill && textarea) {
                                textarea.value = quill.root.innerHTML;
                                console.log(`✅ Updated textarea for answer ${i}`);
                            }
                        }
                    };

                    form.addEventListener('submit', window.pilihanGandaSubmitHandler);
                }

                console.log('✅ Pilihan ganda Quill editors initialization completed');
                return true;

            } catch (error) {
                console.error('❌ Error in initializePilihanGandaQuillEditors:', error);
                return false;
            }
        };

        // Auto-initialize when DOM is ready (for direct page access)
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                window.initializeQuillEditors();
            }, 200);
        });
    </script>
</body>

</html>
