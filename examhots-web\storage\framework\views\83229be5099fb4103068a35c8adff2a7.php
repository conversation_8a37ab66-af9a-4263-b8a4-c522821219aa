
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
    <div class="flex items-center space-x-4">
        <div
            class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                </path>
            </svg>
        </div>

        <div>
            <h3 class="text-xl font-semibold text-gray-900"><PERSON><PERSON><PERSON></h3>
            <p class="text-gray-600 text-sm mt-1">Per<PERSON><PERSON> yang membutuhkan jawaban singkat dan ringkas, bias<PERSON>a
                dalam satu kata</p>
        </div>
    </div>

    <!-- Close Button -->
    <button id="closeQuestionTypeModal" onclick="closeModal()" type="button"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>

<div class="px-6 py-2">
    <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-primary-blue h-2 rounded-full w-full"></div>
    </div>
</div>

<form
    action="<?php echo e($mode === 'edit'
        ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
        : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type])); ?>"
    method="POST" autocomplete="off">
    <?php echo csrf_field(); ?>
    <?php if($mode === 'edit'): ?>
        <?php echo method_field('PUT'); ?>
    <?php endif; ?>

    <div class="px-6 py-6 space-y-4">
        <input type="hidden" name="uploaded_images" id="uploadedImages"
            value="<?php echo e($mode === 'edit' ? $question->img : ''); ?>">

        <div id="myDropzone" class="dropzone mb-3"></div>

        <div class="mb-3">
            <label for="question_text" class="block text-sm font-medium text-gray-700">
                Pertanyaan <span class="text-red-500">*</span>
            </label>
            <div id="question_editor" style="height: 200px;"></div>
            <textarea name="question" id="question_text" style="display: none;"><?php echo e(old('question', $question->question ?? '')); ?></textarea>
            <?php $__errorArgs = ['question'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-3">
            <label for="answer_text" class="block text-sm font-medium text-gray-700">
                Jawaban <span class="text-red-500">*</span>
            </label>
            <div id="answer_editor" style="height: 200px;"></div>
            <textarea name="answer" id="answer_text" style="display: none;"><?php echo old('answer', $question->answers[0]->answer ?? ''); ?></textarea>
            <?php $__errorArgs = ['answer'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>

    <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
        <button id="cancelQuestionType" onclick="closeModal()" type="button"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="continueQuestionType" type="submit"
            class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50">
            <?php echo e($mode === 'edit' ? 'Simpan Perubahan' : 'Simpan'); ?>

        </button>
    </div>
</form>

<script>
    // Initialize Quill editors when this script loads (for direct page access or modal)
    document.addEventListener('DOMContentLoaded', function() {
        // Add a delay to ensure DOM is fully ready
        setTimeout(function() {
            if (typeof window.initializeQuillEditors === 'function') {
                window.initializeQuillEditors();
            }
        }, 300);
    });

    // Also try to initialize immediately if DOM is already ready
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(function() {
            if (typeof window.initializeQuillEditors === 'function') {
                window.initializeQuillEditors();
            }
        }, 300);
    }
</script>
<?php /**PATH C:\xampp-8.2-new\htdocs\examhots\examhots-web\resources\views/admin/exam/questionbank/form/form_uraian_singkat.blade.php ENDPATH**/ ?>