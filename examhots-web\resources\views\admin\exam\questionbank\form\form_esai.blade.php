<div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
    <div class="flex items-center space-x-4">
        <!-- Question <PERSON> -->
        <div
            class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                </path>
            </svg>
        </div>
        <div>
            <h3 class="text-xl font-semibold text-gray-900">Esai</h3>
            <p class="text-gray-600 text-sm mt-1">Pertanyaan yang membutuhkan jawaban yang lebih panjang dan rinci</p>
        </div>
    </div>
    <!-- Close Button -->
    <button id="closeQuestionTypeModal" onclick="closeModal()" type="button"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
            </path>
        </svg>
    </button>
</div>

<div class="px-6 py-2">
    <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="bg-primary-blue h-2 rounded-full w-full"></div>
    </div>
</div>

<form
    action="{{ $mode === 'edit'
        ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
        : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type]) }}"
    method="POST" autocomplete="off">
    @csrf
    @if ($mode === 'edit')
        @method('PUT')
    @endif

    <div class="px-6 py-6 space-y-4">
        <input type="hidden" name="uploaded_images" id="uploadedImages"
            value="{{ $mode === 'edit' ? $question->img : '' }}">

        <div id="myDropzone" class="dropzone mb-3"></div>

        <div class="mb-3">
            <label for="question_text" class="block text-sm font-medium text-gray-700">
                Pertanyaan <span class="text-red-500">*</span>
            </label>
            <div id="question_editor" style="height: 200px;"></div>
            <textarea name="question" id="question_text" style="display: none;">{{ old('question', $question->question ?? '') }}</textarea>
        </div>

        <div class="mb-3">
            <label for="answer_text" class="block text-sm font-medium text-gray-700">
                Jawaban <span class="text-red-500">*</span>
            </label>
            <div id="answer_editor" style="height: 200px;"></div>
            <textarea name="answer" id="answer_text" style="display: none;">{!! old('answer', $question->answers[0]->answer ?? '') !!}</textarea>
        </div>

        <div class="mb-3">
            <label for="score" class="block text-sm font-medium text-gray-700">
                Score Esai <span class="text-red-500">*</span>
            </label>
            <input type="number" name="score" id="score"
                class="mt-1 block w-full rounded-lg shadow-sm text-sm text-gray-900" placeholder="Masukkan Score Esai"
                value="{{ old('score', $question->answers[0]->score ?? '') }}">
        </div>
    </div>

    <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
        <button id="cancelQuestionType" onclick="closeModal()" type="button"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="continueQuestionType" type="submit"
            class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50">
            {{ $mode === 'edit' ? 'Simpan Perubahan' : 'Simpan' }}
        </button>
    </div>
</form>
