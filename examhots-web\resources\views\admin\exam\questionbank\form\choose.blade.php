{{-- <!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>{{ $title ?? 'Modernize' }}</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Mordenize" />
    <meta name="author" content="" />
    <meta name="keywords" content="Mordenize" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="{{ asset('package/dist/images/logos/favicon.png') }} " />
    <!-- Owl Carousel  -->
    <link rel="stylesheet"
        href="{{ asset('package/dist/libs/owl.carousel/package/dist/assets/owl.carousel.min.css') }}">

    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="{{ asset('package/dist/css/style.min.css') }}" />
</head>

<body>
    <div class="container">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden mt-2">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Bank Soal</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Detail Bank
                                        Soal</a></li>
                                <li class="breadcrumb-item" aria-current="page">Pilih Tipe Soal</li>
                            </ol>
                        </nav>
                    </div>

                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h5>Pilih Tipe Soal</h5>
                                <span class="card-subtitle fw-light">Pilih tipe soal yang akan ditambahkan</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <form action="{{  route('question.detail.choose', $question->id) }}" method="POST">
            @csrf
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <!-- Card di dalam card -->
                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <!-- Kiri: Judul + Ikon -->
                                        <div class="d-flex align-items-start gap-3">
                                            <!-- Ikon -->
                                            <i class="ti ti-list" style="font-size: 54px"></i>

                                            <!-- Teks: Judul + Subtitle -->
                                            <div class="d-flex flex-column">
                                                <h6 class="card-title mb-1">Pilihan Ganda</h6>
                                                <span class="card-subtitle text-muted">Pertanyaan dengan beberapa opsi
                                                    jawaban. Siswa memilih satu jawaban yang paling tepat.</span>
                                            </div>
                                        </div>


                                        <!-- Kanan: Radio Input -->
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="type"
                                                    id="type_multiple" value="pilihan_ganda">
                                                <label class="form-check-label" for="type_multiple">Pilih</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <!-- Kiri: Judul + Ikon -->
                                        <div class="d-flex align-items-start gap-3">
                                            <!-- Ikon -->
                                            <i class="ti ti-message-question" style="font-size: 54px"></i>

                                            <!-- Teks: Judul + Subtitle -->
                                            <div class="d-flex flex-column">
                                                <h6 class="card-title mb-1">Uraian Singkat</h6>
                                                <span class="card-subtitle text-muted">Pertanyaan yang membutuhkan
                                                    jawaban
                                                    singkat dan ringkas, biasanya dalam Satu kata.</span>
                                            </div>
                                        </div>


                                        <!-- Kanan: Radio Input -->
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="type"
                                                    id="type-short" value="uraian_singkat">
                                                <label class="form-check-label" for="type-short">Pilih</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <!-- Kiri: Judul + Ikon -->
                                        <div class="d-flex align-items-start gap-3">
                                            <!-- Ikon -->
                                            <i class="ti ti-file-text" style="font-size: 54px"></i>

                                            <!-- Teks: Judul + Subtitle -->
                                            <div class="d-flex flex-column">
                                                <h6 class="card-title mb-1">Esai</h6>
                                                <span class="card-subtitle text-muted">Pertanyaan yang membutuhkan
                                                    jawaban
                                                    yang lebih panjang dan rinci.</span>
                                            </div>
                                        </div>


                                        <!-- Kanan: Radio Input -->
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="type"
                                                    id="type_esai" value="esai">
                                                <label class="form-check-label" for="type_esai">Pilih</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-end gap-3">
                                <a href="{{ route('question.detail', $question->id) }}" class="btn btn-danger"><i
                                        class="ti ti-arrow-left"></i>
                                    Kembali</a>
                                <button class="btn btn-primary">Lanjut Buat Soal <i class="ti ti-send"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!--  Customizer -->
    <!--  Import Js Files -->
    <script src="{{ asset('package/dist/libs/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/simplebar/dist/simplebar.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
    <!--  core files -->
    <script src="{{ asset('package/dist/js/app.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/app.init.js') }}"></script>
    <script src="{{ asset('package/dist/js/app-style-switcher.js') }}"></script>
    <script src="{{ asset('package/dist/js/sidebarmenu.js') }}"></script>
    <script src="{{ asset('package/dist/js/custom.js') }}"></script>
    <!--  current page js files -->
    <script src="{{ asset('package/dist/libs/owl.carousel/dist/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/dashboard.js') }}"></script>

    @yield('script')
</body>

</html> --}}
<form id="chooseTypeForm" action="{{ route('question.detail.choose', $question->id) }}" method="POST">
    @csrf
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
        <div class="flex items-center space-x-4">
            <!-- Question Mark Icon -->
            <div class="w-12 h-12 bg-primary-blue rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z" />
                </svg>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-900">Pilih Tipe Pertanyaan</h3>
                <p class="text-gray-600 text-sm mt-1">Tentukan format soal yang ingin kamu buat.</p>
            </div>
        </div>
        <!-- Close Button -->
        <button id="closeQuestionTypeModal" onclick="closeModal()" type="button"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                </path>
            </svg>
        </button>
    </div>

    <!-- Progress Bar -->
    <div class="px-6 py-2">
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-primary-blue h-2 rounded-full w-1/2"></div>
        </div>
    </div>

    <!-- Modal Body -->
    <div class="px-6 py-6 space-y-4">
        <!-- Pilihan Ganda Option -->
        <label
            class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
            <input type="radio" name="type" value="pilihan_ganda" class="hidden question-type-radio">
            <div class="flex items-center space-x-4 flex-1">
                <!-- Icon -->
                <div
                    class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 text-lg">Pilihan Ganda</h4>
                    <p class="text-gray-600 text-sm">Pertanyaan dengan beberapa opsi jawaban. Siswa memilih satu
                        jawaban yang paling tepat.</p>
                </div>
            </div>
            <!-- Radio Button -->
            <div
                class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
            </div>
        </label>

        <!-- Uraian Singkat Option -->
        <label
            class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
            <input type="radio" name="type" value="uraian_singkat" class="hidden question-type-radio">
            <div class="flex items-center space-x-4 flex-1">
                <!-- Icon -->
                <div
                    class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                        </path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 text-lg">Uraian Singkat</h4>
                    <p class="text-gray-600 text-sm">Pertanyaan yang membutuhkan jawaban singkat dan ringkas,
                        biasanya dalam satu kata.</p>
                </div>
            </div>
            <!-- Radio Button -->
            <div
                class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
            </div>
        </label>

        <!-- Essay Option -->
        <label
            class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
            <input type="radio" name="type" value="esai" class="hidden question-type-radio">
            <div class="flex items-center space-x-4 flex-1">
                <!-- Icon -->
                <div
                    class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                        </path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 text-lg">Esai</h4>
                    <p class="text-gray-600 text-sm">Pertanyaan yang membutuhkan jawaban yang lebih panjang dan
                        rinci.</p>
                </div>
            </div>
            <!-- Radio Button -->
            <div
                class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
            </div>
        </label>
    </div>

    <!-- Modal Footer -->
    <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
        <button id="cancelQuestionType" onclick="closeModal()" type="button"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
            Batal
        </button>
        <button id="continueQuestionType" type="submit"
            class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            disabled>
            Lanjut Buat Soal
        </button>
    </div>
</form>
